"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { vehicles } from "@/lib/data";
import { Vehicle } from "@/lib/types";

export default function EVListingsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMake, setSelectedMake] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 150000]);
  const [rangeFilter, setRangeFilter] = useState<[number, number]>([0, 500]);
  const [compareList, setCompareList] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<"price" | "range" | "name">("name");

  // Get unique makes and types for filters
  const makes = Array.from(new Set(vehicles.map((v) => v.make))).sort();
  const types = Array.from(new Set(vehicles.map((v) => v.type))).sort();

  // Filter and sort vehicles
  const filteredVehicles = vehicles
    .filter((vehicle) => {
      const matchesSearch =
        vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vehicle.model.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesMake = !selectedMake || vehicle.make === selectedMake;
      const matchesType = !selectedType || vehicle.type === selectedType;
      const matchesPrice =
        vehicle.price.msrp >= priceRange[0] &&
        vehicle.price.msrp <= priceRange[1];
      const matchesRange =
        vehicle.range >= rangeFilter[0] && vehicle.range <= rangeFilter[1];

      return (
        matchesSearch &&
        matchesMake &&
        matchesType &&
        matchesPrice &&
        matchesRange
      );
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "price":
          return a.price.msrp - b.price.msrp;
        case "range":
          return b.range - a.range;
        case "name":
        default:
          return `${a.make} ${a.model}`.localeCompare(`${b.make} ${b.model}`);
      }
    });

  const toggleCompare = (vehicleId: string) => {
    setCompareList((prev) =>
      prev.includes(vehicleId)
        ? prev.filter((id) => id !== vehicleId)
        : prev.length < 3
        ? [...prev, vehicleId]
        : prev
    );
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedMake(null);
    setSelectedType(null);
    setPriceRange([0, 150000]);
    setRangeFilter([0, 500]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Electric Vehicle Database
          </h1>
          <p className="text-gray-600 mb-6">
            Explore and compare the latest electric vehicles. Find detailed
            specifications, pricing, and features.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Filters
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    Clear All
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Search */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Search
                  </label>
                  <Input
                    type="search"
                    placeholder="Search vehicles..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    icon={
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                    }
                  />
                </div>

                {/* Make Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Make
                  </label>
                  <select
                    value={selectedMake || ""}
                    onChange={(e) => setSelectedMake(e.target.value || null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">All Makes</option>
                    {makes.map((make) => (
                      <option key={make} value={make}>
                        {make}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vehicle Type
                  </label>
                  <select
                    value={selectedType || ""}
                    onChange={(e) => setSelectedType(e.target.value || null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">All Types</option>
                    {types.map((type) => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Price Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price Range: ${priceRange[0].toLocaleString()} - $
                    {priceRange[1].toLocaleString()}
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="0"
                      max="150000"
                      step="5000"
                      value={priceRange[0]}
                      onChange={(e) =>
                        setPriceRange([parseInt(e.target.value), priceRange[1]])
                      }
                      className="w-full"
                    />
                    <input
                      type="range"
                      min="0"
                      max="150000"
                      step="5000"
                      value={priceRange[1]}
                      onChange={(e) =>
                        setPriceRange([priceRange[0], parseInt(e.target.value)])
                      }
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Electric Range: {rangeFilter[0]} - {rangeFilter[1]} miles
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="0"
                      max="500"
                      step="10"
                      value={rangeFilter[0]}
                      onChange={(e) =>
                        setRangeFilter([
                          parseInt(e.target.value),
                          rangeFilter[1],
                        ])
                      }
                      className="w-full"
                    />
                    <input
                      type="range"
                      min="0"
                      max="500"
                      step="10"
                      value={rangeFilter[1]}
                      onChange={(e) =>
                        setRangeFilter([
                          rangeFilter[0],
                          parseInt(e.target.value),
                        ])
                      }
                      className="w-full"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Compare Panel */}
            {compareList.length > 0 && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>
                    Compare Vehicles ({compareList.length}/3)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {compareList.map((vehicleId) => {
                      const vehicle = vehicles.find((v) => v.id === vehicleId);
                      return vehicle ? (
                        <div
                          key={vehicleId}
                          className="flex items-center justify-between text-sm"
                        >
                          <span>
                            {vehicle.make} {vehicle.model}
                          </span>
                          <button
                            onClick={() => toggleCompare(vehicleId)}
                            className="text-red-600 hover:text-red-800"
                          >
                            ×
                          </button>
                        </div>
                      ) : null;
                    })}
                  </div>
                  {compareList.length >= 2 && (
                    <Button className="w-full mt-4">Compare Selected</Button>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Vehicle Grid */}
          <div className="lg:col-span-3">
            {/* Sort and Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <span className="text-gray-600">
                  {filteredVehicles.length} vehicles found
                </span>
                <select
                  value={sortBy}
                  onChange={(e) =>
                    setSortBy(e.target.value as "price" | "range" | "name")
                  }
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
                >
                  <option value="name">Sort by Name</option>
                  <option value="price">Sort by Price</option>
                  <option value="range">Sort by Range</option>
                </select>
              </div>
            </div>

            {/* Vehicle Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredVehicles.map((vehicle) => (
                <Card
                  key={vehicle.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <div className="relative">
                    <div className="relative h-48 rounded-t-xl overflow-hidden">
                      <Image
                        src={vehicle.images[0]}
                        alt={`${vehicle.make} ${vehicle.model}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="absolute top-2 right-2">
                      <Badge
                        variant={
                          vehicle.availability === "available"
                            ? "success"
                            : "warning"
                        }
                      >
                        {vehicle.availability === "available"
                          ? "Available"
                          : "Coming Soon"}
                      </Badge>
                    </div>
                    <div className="absolute top-2 left-2">
                      <button
                        onClick={() => toggleCompare(vehicle.id)}
                        className={`px-2 py-1 rounded text-xs font-medium ${
                          compareList.includes(vehicle.id)
                            ? "bg-primary-600 text-white"
                            : "bg-white text-gray-700 hover:bg-gray-100"
                        }`}
                        disabled={
                          !compareList.includes(vehicle.id) &&
                          compareList.length >= 3
                        }
                      >
                        {compareList.includes(vehicle.id)
                          ? "Remove"
                          : "Compare"}
                      </button>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {vehicle.year} {vehicle.make} {vehicle.model}
                      </h3>
                      {vehicle.trim && (
                        <p className="text-sm text-gray-600">{vehicle.trim}</p>
                      )}
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Starting MSRP:</span>
                        <span className="font-semibold">
                          ${vehicle.price.msrp.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Range:</span>
                        <span className="font-semibold">
                          {vehicle.range} miles
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Battery:</span>
                        <span className="font-semibold">
                          {vehicle.batteryCapacity} kWh
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">0-60 mph:</span>
                        <span className="font-semibold">
                          {vehicle.specifications.acceleration}s
                        </span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button className="flex-1" size="sm">
                        <Link href={`/ev-listings/${vehicle.id}`}>
                          View Details
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm">
                        Find Dealers
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredVehicles.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500 mb-4">
                  No vehicles found matching your criteria.
                </p>
                <Button onClick={clearFilters}>Clear Filters</Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
