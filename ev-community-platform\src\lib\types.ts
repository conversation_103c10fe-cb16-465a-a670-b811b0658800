// User types
export interface User {
  id: string;
  username: string;
  email: string;
  avatar: string;
  joinDate: string;
  location: string;
  bio: string;
  postCount: number;
  reputation: number;
  isVerified: boolean;
  role: 'member' | 'moderator' | 'admin' | 'business';
}

// Vehicle types
export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  trim?: string;
  type: 'sedan' | 'suv' | 'hatchback' | 'coupe' | 'truck' | 'van';
  batteryCapacity: number; // kWh
  range: number; // miles
  chargingSpeed: number; // kW
  price: {
    msrp: number;
    used?: number;
  };
  images: string[];
  specifications: {
    motorPower: number; // hp
    acceleration: number; // 0-60 mph
    topSpeed: number; // mph
    efficiency: number; // miles/kWh
    chargingTime: {
      level1: number; // hours
      level2: number; // hours
      dcFast: number; // minutes to 80%
    };
    dimensions: {
      length: number;
      width: number;
      height: number;
      wheelbase: number;
    };
    weight: number; // lbs
    seatingCapacity: number;
    cargoSpace: number; // cubic feet
  };
  features: string[];
  availability: 'available' | 'coming-soon' | 'discontinued';
}

// Forum types
export interface ForumCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  threadCount: number;
  postCount: number;
  lastActivity: {
    threadId: string;
    threadTitle: string;
    userId: string;
    username: string;
    timestamp: string;
  };
}

export interface ForumThread {
  id: string;
  categoryId: string;
  title: string;
  content: string;
  authorId: string;
  authorUsername: string;
  authorAvatar: string;
  createdAt: string;
  updatedAt: string;
  replyCount: number;
  viewCount: number;
  isSticky: boolean;
  isLocked: boolean;
  tags: string[];
  lastReply?: {
    userId: string;
    username: string;
    timestamp: string;
  };
}

export interface ForumPost {
  id: string;
  threadId: string;
  content: string;
  authorId: string;
  authorUsername: string;
  authorAvatar: string;
  createdAt: string;
  updatedAt?: string;
  likes: number;
  isEdited: boolean;
  parentPostId?: string; // for replies
}

// Marketplace types
export interface MarketplaceItem {
  id: string;
  type: 'vehicle' | 'part' | 'accessory' | 'service';
  title: string;
  description: string;
  price: number;
  currency: string;
  condition: 'new' | 'used' | 'refurbished';
  images: string[];
  sellerId: string;
  sellerUsername: string;
  sellerRating: number;
  location: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'sold' | 'pending' | 'inactive';
  category: string;
  subcategory?: string;
  vehicleDetails?: {
    vehicleId: string;
    mileage: number;
    vin?: string;
    batteryHealth?: number; // percentage
    accidentHistory: boolean;
    serviceHistory: boolean;
  };
  partDetails?: {
    compatibility: string[];
    partNumber?: string;
    warranty?: string;
  };
}

// Business types
export interface Business {
  id: string;
  name: string;
  description: string;
  category: 'dealership' | 'service' | 'charging' | 'insurance' | 'parts' | 'other';
  subcategory: string;
  logo: string;
  images: string[];
  contact: {
    phone: string;
    email: string;
    website?: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  hours: {
    [key: string]: {
      open: string;
      close: string;
      closed?: boolean;
    };
  };
  rating: number;
  reviewCount: number;
  verified: boolean;
  services: string[];
  certifications: string[];
  createdAt: string;
}

// Garage types
export interface GarageVehicle {
  id: string;
  ownerId: string;
  vehicleId: string;
  nickname?: string;
  purchaseDate: string;
  purchasePrice?: number;
  currentMileage: number;
  modifications: string[];
  images: string[];
  notes: string;
  isPublic: boolean;
  batteryHealth?: number;
  maintenanceRecords: MaintenanceRecord[];
}

export interface MaintenanceRecord {
  id: string;
  date: string;
  type: string;
  description: string;
  cost?: number;
  mileage: number;
  serviceProvider?: string;
}

// Activity types
export interface Activity {
  id: string;
  type: 'forum_post' | 'marketplace_listing' | 'garage_update' | 'new_member' | 'review';
  userId: string;
  username: string;
  userAvatar: string;
  title: string;
  description: string;
  timestamp: string;
  relatedId?: string; // ID of related item (thread, listing, etc.)
  relatedType?: string;
  metadata?: Record<string, any>;
}
