"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { forumCategories, forumThreads } from "@/lib/data";

export default function ForumsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const filteredThreads = forumThreads.filter((thread) => {
    const matchesSearch =
      thread.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      thread.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      !selectedCategory || thread.categoryId === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getCategoryName = (categoryId: string) => {
    return (
      forumCategories.find((cat) => cat.id === categoryId)?.name || "Unknown"
    );
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = Math.floor(
      (now.getTime() - time.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Community Forums
          </h1>
          <p className="text-gray-600 mb-6">
            Connect with fellow EV enthusiasts, share experiences, and get
            answers to your questions.
          </p>

          {/* Search and Actions */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Input
                type="search"
                placeholder="Search discussions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                }
              />
            </div>
            <Button>Start New Discussion</Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Categories Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <button
                    onClick={() => setSelectedCategory(null)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                      !selectedCategory
                        ? "bg-primary-100 text-primary-800"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    All Categories
                  </button>
                  {forumCategories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                        selectedCategory === category.id
                          ? "bg-primary-100 text-primary-800"
                          : "hover:bg-gray-100"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{category.icon}</span>
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <span className="text-sm text-gray-500">
                          {category.threadCount}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        {category.description}
                      </p>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Forum Stats */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Forum Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Threads:</span>
                    <span className="font-semibold">
                      {forumCategories
                        .reduce((sum, cat) => sum + cat.threadCount, 0)
                        .toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Posts:</span>
                    <span className="font-semibold">
                      {forumCategories
                        .reduce((sum, cat) => sum + cat.postCount, 0)
                        .toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Active Today:</span>
                    <span className="font-semibold">247</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Threads List */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>
                    {selectedCategory
                      ? `${getCategoryName(selectedCategory)} Discussions`
                      : "Recent Discussions"}
                  </span>
                  <span className="text-sm font-normal text-gray-500">
                    {filteredThreads.length} threads
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredThreads.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-500">
                        No discussions found matching your criteria.
                      </p>
                      <Button className="mt-4">
                        Start the First Discussion
                      </Button>
                    </div>
                  ) : (
                    filteredThreads.map((thread) => (
                      <div
                        key={thread.id}
                        className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-start space-x-4">
                          <Image
                            src={thread.authorAvatar}
                            alt={thread.authorUsername}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  {thread.isSticky && (
                                    <Badge variant="warning" size="sm">
                                      Pinned
                                    </Badge>
                                  )}
                                  {thread.isLocked && (
                                    <Badge variant="danger" size="sm">
                                      Locked
                                    </Badge>
                                  )}
                                  <Badge variant="outline" size="sm">
                                    {getCategoryName(thread.categoryId)}
                                  </Badge>
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                  <Link
                                    href={`/forums/thread/${thread.id}`}
                                    className="hover:text-primary-600"
                                  >
                                    {thread.title}
                                  </Link>
                                </h3>
                                <p className="text-gray-600 text-sm line-clamp-2 mb-2">
                                  {thread.content}
                                </p>
                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <span>by {thread.authorUsername}</span>
                                  <span>{formatTimeAgo(thread.createdAt)}</span>
                                  <span>{thread.replyCount} replies</span>
                                  <span>{thread.viewCount} views</span>
                                </div>
                              </div>
                              {thread.lastReply && (
                                <div className="text-right text-sm text-gray-500 ml-4">
                                  <p>Last reply by</p>
                                  <p className="font-medium">
                                    {thread.lastReply.username}
                                  </p>
                                  <p>
                                    {formatTimeAgo(thread.lastReply.timestamp)}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
