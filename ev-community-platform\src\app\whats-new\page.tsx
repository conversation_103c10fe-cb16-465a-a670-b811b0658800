import React from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { recentActivity } from "@/lib/data";

export default function WhatsNewPage() {
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = Math.floor(
      (now.getTime() - time.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "forum_post":
        return "💬";
      case "marketplace_listing":
        return "🛒";
      case "garage_update":
        return "🚗";
      case "new_member":
        return "👋";
      case "review":
        return "⭐";
      default:
        return "📝";
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case "forum_post":
        return "bg-blue-100 text-blue-800";
      case "marketplace_listing":
        return "bg-green-100 text-green-800";
      case "garage_update":
        return "bg-purple-100 text-purple-800";
      case "new_member":
        return "bg-yellow-100 text-yellow-800";
      case "review":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">What's New</h1>
          <p className="text-gray-600 mb-6">
            Stay up to date with the latest activity in the EV community.
          </p>
        </div>

        {/* Activity Feed */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {recentActivity.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start space-x-4 pb-6 border-b border-gray-200 last:border-b-0 last:pb-0"
                >
                  <Image
                    src={activity.userAvatar}
                    alt={activity.username}
                    width={48}
                    height={48}
                    className="rounded-full"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">
                        {getActivityIcon(activity.type)}
                      </span>
                      <Badge
                        variant="outline"
                        className={getActivityColor(activity.type)}
                      >
                        {activity.type
                          .replace("_", " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {formatTimeAgo(activity.timestamp)}
                      </span>
                    </div>
                    <div className="mb-2">
                      <span className="font-medium text-gray-900">
                        {activity.username}
                      </span>
                      <span className="text-gray-600 ml-1">
                        {activity.title}
                      </span>
                    </div>
                    <p className="text-gray-700 mb-3">{activity.description}</p>
                    {activity.relatedId && (
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                        {activity.type === "forum_post" && (
                          <Button variant="ghost" size="sm">
                            Join Discussion
                          </Button>
                        )}
                        {activity.type === "marketplace_listing" && (
                          <Button variant="ghost" size="sm">
                            View Listing
                          </Button>
                        )}
                        {activity.type === "garage_update" && (
                          <Button variant="ghost" size="sm">
                            View Garage
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 text-center">
              <Button variant="outline">Load More Activity</Button>
            </div>
          </CardContent>
        </Card>

        {/* Trending Topics */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Trending Topics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">
                  🔥 Hot Discussions
                </h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    <Link
                      href="/forums"
                      className="text-primary-600 hover:underline"
                    >
                      Tesla Model 3 Highland Review
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/forums"
                      className="text-primary-600 hover:underline"
                    >
                      Best Home Charging Setup 2024
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/forums"
                      className="text-primary-600 hover:underline"
                    >
                      EV Road Trip Planning Tips
                    </Link>
                  </li>
                </ul>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">
                  📈 Popular This Week
                </h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    <Link
                      href="/ev-listings"
                      className="text-primary-600 hover:underline"
                    >
                      BMW iX vs Mercedes EQS
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/marketplace"
                      className="text-primary-600 hover:underline"
                    >
                      Used EV Market Trends
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/garage"
                      className="text-primary-600 hover:underline"
                    >
                      Battery Health Monitoring
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
