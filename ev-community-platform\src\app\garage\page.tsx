import React from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { vehicles, users } from "@/lib/data";

export default function GaragePage() {
  // Mock garage data - in real app this would come from API
  const garageVehicles = [
    {
      id: "1",
      ownerId: "1",
      vehicleId: "1",
      nickname: "Lightning",
      purchaseDate: "2023-06-15",
      currentMileage: 25000,
      images: [
        "https://images.unsplash.com/photo-**********-b8a1929cea89?w=800&h=600&fit=crop",
      ],
      notes: "My daily driver. Love the efficiency and tech features!",
      isPublic: true,
      batteryHealth: 96,
    },
    {
      id: "2",
      ownerId: "3",
      vehicleId: "2",
      nickname: "Blue Thunder",
      purchaseDate: "2024-01-20",
      currentMileage: 8500,
      images: [
        "https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=800&h=600&fit=crop",
      ],
      notes: "Family SUV that handles everything we throw at it.",
      isPublic: true,
      batteryHealth: 98,
    },
    {
      id: "3",
      ownerId: "6",
      vehicleId: "3",
      nickname: "Mustang",
      purchaseDate: "2024-03-10",
      currentMileage: 5200,
      images: [
        "https://images.unsplash.com/photo-1617788138017-80ad40651399?w=800&h=600&fit=crop",
      ],
      notes: "Perfect for road trips. The range is impressive!",
      isPublic: true,
      batteryHealth: 100,
    },
  ];

  const getVehicleInfo = (vehicleId: string) => {
    return vehicles.find((v) => v.id === vehicleId);
  };

  const getOwnerInfo = (ownerId: string) => {
    return users.find((u) => u.id === ownerId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Community Garage
          </h1>
          <p className="text-gray-600 mb-6">
            Showcase your electric vehicle and explore what others are driving.
          </p>
          <Button>Add Your Vehicle</Button>
        </div>

        {/* Featured Garages */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {garageVehicles.map((garageVehicle) => {
            const vehicle = getVehicleInfo(garageVehicle.vehicleId);
            const owner = getOwnerInfo(garageVehicle.ownerId);

            if (!vehicle || !owner) return null;

            return (
              <Card
                key={garageVehicle.id}
                className="hover:shadow-lg transition-shadow"
              >
                <div className="relative">
                  <div className="relative h-48 rounded-t-xl overflow-hidden">
                    <Image
                      src={garageVehicle.images[0]}
                      alt={
                        garageVehicle.nickname ||
                        `${vehicle.make} ${vehicle.model}`
                      }
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="absolute top-2 right-2">
                    <Badge variant="success">
                      {garageVehicle.batteryHealth}% Battery Health
                    </Badge>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">
                      {garageVehicle.nickname}
                    </h3>
                    <p className="text-gray-600">
                      {vehicle.year} {vehicle.make} {vehicle.model}
                    </p>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Owner:</span>
                      <span className="font-medium">{owner.username}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Mileage:</span>
                      <span>
                        {garageVehicle.currentMileage.toLocaleString()} miles
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Owned Since:</span>
                      <span>
                        {new Date(
                          garageVehicle.purchaseDate
                        ).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {garageVehicle.notes}
                  </p>

                  <div className="flex space-x-2">
                    <Button className="flex-1" size="sm">
                      <Link href={`/garage/${garageVehicle.id}`}>
                        View Garage
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm">
                      <Link href={`/profile/${owner.id}`}>View Profile</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>Garage Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600">
                    1,567
                  </div>
                  <div className="text-sm text-gray-600">
                    Vehicles Showcased
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600">847</div>
                  <div className="text-sm text-gray-600">Active Members</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600">
                    2.4M
                  </div>
                  <div className="text-sm text-gray-600">
                    Total Miles Driven
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600">94%</div>
                  <div className="text-sm text-gray-600">
                    Avg Battery Health
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
