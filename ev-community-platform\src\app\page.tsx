import React from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { quickStats, recentActivity, featuredContent } from "@/lib/data";

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Welcome to the{" "}
              <span className="text-primary-200">EV Community</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
              Connect with electric vehicle enthusiasts, discover the latest
              EVs, buy and sell in our marketplace, and showcase your electric
              journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary">
                Join the Community
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                Explore EVs
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Stats */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-6 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {quickStats.totalMembers.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Members</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {quickStats.totalThreads.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Discussions</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {quickStats.totalMessages.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Messages</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {quickStats.totalVehicles.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Vehicles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {quickStats.totalListings.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Listings</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">
                {quickStats.totalBusinesses.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Businesses</div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Featured Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Featured Discussion */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Featured Discussion
                    <Badge variant="primary">Hot</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900">
                      <Link
                        href={`/forums/thread/${featuredContent.featuredThread.id}`}
                        className="hover:text-primary-600"
                      >
                        {featuredContent.featuredThread.title}
                      </Link>
                    </h3>
                    <p className="text-gray-600">
                      {featuredContent.featuredThread.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>by {featuredContent.featuredThread.author}</span>
                      <div className="flex space-x-4">
                        <span>
                          {featuredContent.featuredThread.replies} replies
                        </span>
                        <span>
                          {featuredContent.featuredThread.views} views
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Featured Vehicle */}
              <Card>
                <CardHeader>
                  <CardTitle>Featured Vehicle</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="relative h-48 rounded-lg overflow-hidden">
                      <Image
                        src={featuredContent.featuredVehicle.image}
                        alt={`${featuredContent.featuredVehicle.make} ${featuredContent.featuredVehicle.model}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold">
                        {featuredContent.featuredVehicle.year}{" "}
                        {featuredContent.featuredVehicle.make}{" "}
                        {featuredContent.featuredVehicle.model}
                      </h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Starting MSRP:</span>
                          <span className="font-semibold">
                            $
                            {featuredContent.featuredVehicle.price.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Range:</span>
                          <span className="font-semibold">
                            {featuredContent.featuredVehicle.range} miles
                          </span>
                        </div>
                      </div>
                      <Button className="w-full">
                        <Link
                          href={`/ev-listings/${featuredContent.featuredVehicle.id}`}
                        >
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* What's New Feed */}
              <Card>
                <CardHeader>
                  <CardTitle>What's New</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentActivity.slice(0, 5).map((activity) => (
                      <div
                        key={activity.id}
                        className="flex items-start space-x-3"
                      >
                        <Image
                          src={activity.userAvatar}
                          alt={activity.username}
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {activity.username}
                          </p>
                          <p className="text-sm text-gray-600">
                            {activity.description}
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(activity.timestamp).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4">
                    <Button variant="outline" className="w-full">
                      <Link href="/whats-new">View All Activity</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Featured Marketplace Listing */}
              <Card>
                <CardHeader>
                  <CardTitle>Featured Listing</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="relative h-32 rounded-lg overflow-hidden">
                      <Image
                        src={featuredContent.featuredListing.image}
                        alt={featuredContent.featuredListing.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <h3 className="font-semibold text-gray-900 line-clamp-2">
                      {featuredContent.featuredListing.title}
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Price:</span>
                        <span className="font-semibold text-primary-600">
                          $
                          {featuredContent.featuredListing.price.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Mileage:</span>
                        <span>
                          {featuredContent.featuredListing.mileage.toLocaleString()}{" "}
                          miles
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span>{featuredContent.featuredListing.location}</span>
                      </div>
                    </div>
                    <Button className="w-full">
                      <Link
                        href={`/marketplace/${featuredContent.featuredListing.id}`}
                      >
                        View Listing
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
