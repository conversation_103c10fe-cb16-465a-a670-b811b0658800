import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { businesses } from "@/lib/data";

export default function BusinessDirectoryPage() {
  const categories = Array.from(
    new Set(businesses.map((b) => b.category))
  ).sort();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Business Directory
          </h1>
          <p className="text-gray-600 mb-6">
            Find trusted EV dealerships, service centers, charging networks, and
            more.
          </p>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Browse by Category
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {categories.map((category) => (
              <Card
                key={category}
                className="hover:shadow-md transition-shadow cursor-pointer"
              >
                <CardContent className="p-4 text-center">
                  <div className="text-2xl mb-2">
                    {category === "dealership" && "🚗"}
                    {category === "service" && "🔧"}
                    {category === "charging" && "🔌"}
                    {category === "insurance" && "🛡️"}
                    {category === "parts" && "⚙️"}
                  </div>
                  <h3 className="font-medium text-gray-900 capitalize">
                    {category}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {businesses.filter((b) => b.category === category).length}{" "}
                    businesses
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Business Listings */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {businesses.map((business) => (
            <Card
              key={business.id}
              className="hover:shadow-lg transition-shadow"
            >
              <div className="relative h-48 rounded-t-xl overflow-hidden">
                <Image
                  src={business.images[0]}
                  alt={business.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-2 right-2">
                  {business.verified && (
                    <Badge variant="success">Verified</Badge>
                  )}
                </div>
              </div>

              <CardContent className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {business.name}
                  </h3>
                  <p className="text-sm text-gray-600 capitalize">
                    {business.category} • {business.subcategory}
                  </p>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {business.description}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-400">★</span>
                      <span className="font-medium">{business.rating}</span>
                      <span className="text-gray-600">
                        ({business.reviewCount} reviews)
                      </span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    {business.contact.address.city},{" "}
                    {business.contact.address.state}
                  </div>
                  <div className="text-sm text-gray-600">
                    {business.contact.phone}
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button className="flex-1" size="sm">
                    <Link href={`/business-directory/${business.id}`}>
                      View Details
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm">
                    Contact
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
