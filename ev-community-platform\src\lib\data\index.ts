// Export all dummy data
export { users } from './users';
export { vehicles } from './vehicles';
export { forumCategories, forumThreads } from './forums';
export { marketplaceItems } from './marketplace';
export { businesses } from './businesses';

// Quick stats for home page
export const quickStats = {
  totalMembers: 12847,
  totalThreads: 4108,
  totalMessages: 27101,
  totalVehicles: 1567,
  totalListings: 234,
  totalBusinesses: 89,
};

// Recent activity data
export const recentActivity = [
  {
    id: '1',
    type: 'forum_post' as const,
    userId: '1',
    username: 'EVEnthusiast2024',
    userAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    title: 'New post in Tesla forum',
    description: 'Model 3 Highland vs Long Range - Which to choose?',
    timestamp: '2024-07-28T10:30:00Z',
    relatedId: '1',
    relatedType: 'thread',
  },
  {
    id: '2',
    type: 'marketplace_listing' as const,
    userId: '3',
    username: 'GreenDriveFamily',
    userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    title: 'New marketplace listing',
    description: 'Tesla Model 3/Y All-Weather Floor Mats Set',
    timestamp: '2024-07-28T08:30:00Z',
    relatedId: '5',
    relatedType: 'listing',
  },
  {
    id: '3',
    type: 'garage_update' as const,
    userId: '6',
    username: 'SolarPoweredEV',
    userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    title: 'Updated garage',
    description: 'Added new photos to Model Y showcase',
    timestamp: '2024-07-28T07:15:00Z',
    relatedId: '6',
    relatedType: 'garage',
  },
  {
    id: '4',
    type: 'new_member' as const,
    userId: '8',
    username: 'NewEVOwner',
    userAvatar: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=150&h=150&fit=crop&crop=face',
    title: 'New member joined',
    description: 'Welcome to the EV community!',
    timestamp: '2024-07-28T06:45:00Z',
    relatedId: '8',
    relatedType: 'user',
  },
  {
    id: '5',
    type: 'forum_post' as const,
    userId: '4',
    username: 'TechReviewer',
    userAvatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
    title: 'New discussion started',
    description: 'EV adoption rates in 2024',
    timestamp: '2024-07-28T09:00:00Z',
    relatedId: '4',
    relatedType: 'thread',
  },
];

// Featured content for home page
export const featuredContent = {
  featuredThread: {
    id: '2',
    title: 'iX charging speed optimization tips',
    excerpt: 'After 6 months with my iX xDrive50, I\'ve learned some tricks to maximize charging speed...',
    author: 'ChargingStationGuru',
    replies: 18,
    views: 342,
    category: 'BMW',
  },
  featuredVehicle: {
    id: '1',
    make: 'Tesla',
    model: 'Model 3',
    year: 2024,
    image: 'https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=800&h=600&fit=crop',
    price: 47240,
    range: 358,
  },
  featuredListing: {
    id: '1',
    title: '2022 Tesla Model 3 Long Range - Excellent Condition',
    price: 42000,
    image: 'https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=800&h=600&fit=crop',
    location: 'San Francisco, CA',
    mileage: 25000,
  },
};
