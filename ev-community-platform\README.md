# EV Community Platform

A comprehensive electric vehicle community platform built with Next.js, TypeScript, and Tailwind CSS. This platform connects EV owners, enthusiasts, and businesses through forums, marketplace, and showcase features.

## 🚗 Features

### Core Pages

- **Home Page**: Hero section, quick stats, featured content, and activity feed
- **Forums**: Category-based discussions with search and filtering
- **EV Listings**: Comprehensive vehicle database with comparison tools
- **Marketplace**: Buy/sell vehicles, parts, and accessories
- **User Garage**: Vehicle showcase and member profiles
- **Business Directory**: EV-related businesses and services
- **What's New**: Activity feed and trending topics

### Key Functionality

- **Responsive Design**: Mobile-first approach with desktop optimization
- **Search & Filtering**: Advanced filtering across all sections
- **User Profiles**: Member profiles with reputation system
- **Vehicle Comparison**: Side-by-side EV specifications
- **Activity Tracking**: Real-time community activity feed
- **Business Verification**: Verified business listings

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Custom component library
- **Images**: Next.js Image optimization with Unsplash
- **Icons**: SVG icons and emojis

## 🎨 Design System

### Colors

- **Primary**: Electric Blue (#007BFF)
- **Secondary**: Clean grays and whites
- **Accent**: Success green (#28A745)
- **Background**: Light gray (#F8F9FA)

### Typography

- **Font**: Inter (Google Fonts)
- **Base Size**: 16px
- **Scale**: Responsive typography

### Components

- Cards with hover effects
- Buttons with multiple variants
- Form inputs with validation states
- Badges and status indicators
- Navigation with active states

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd ev-community-platform
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Build for Production

```bash
npm run build
npm start
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── business-directory/ # Business directory page
│   ├── ev-listings/       # EV database page
│   ├── forums/            # Forums page
│   ├── garage/            # User garage page
│   ├── marketplace/       # Marketplace page
│   ├── whats-new/         # Activity feed page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── layout/           # Layout components
│   └── ui/               # UI components
└── lib/                  # Utilities and data
    ├── data/             # Dummy data
    ├── types.ts          # TypeScript types
    └── utils.ts          # Utility functions
```

## 📊 Dummy Data

The platform includes comprehensive dummy data:

- **Users**: 10 sample users with different roles
- **Vehicles**: 5 popular EV models with full specifications
- **Forum Posts**: Sample discussions across categories
- **Marketplace Items**: Vehicle and parts listings
- **Businesses**: EV-related service providers
- **Activity Feed**: Recent community activity

## 🎯 Key Features Implemented

### Navigation & Layout

- Responsive header with search functionality
- Mobile-friendly navigation menu
- Footer with organized links
- Sticky navigation for better UX

### Home Page

- Hero section with call-to-action
- Community statistics dashboard
- Featured discussions and vehicles
- Recent activity sidebar

### Forums

- Category-based organization
- Thread search and filtering
- User avatars and reputation
- Sticky and locked thread indicators

### EV Listings

- Advanced filtering (make, type, price, range)
- Vehicle comparison functionality
- Detailed specifications display
- Sort by multiple criteria

### Marketplace

- Multi-type listings (vehicles, parts, accessories)
- Price range filtering
- Seller ratings and verification
- Location-based browsing

### User Garage

- Vehicle showcase with photos
- Battery health monitoring
- Member profiles and statistics
- Community engagement features

### Business Directory

- Category-based business listings
- Verification badges
- Contact information and ratings
- Service area coverage

### What's New

- Real-time activity feed
- Activity type categorization
- Trending topics section
- User engagement tracking

## 🔧 Customization

### Adding New Vehicle Data

Edit `src/lib/data/vehicles.ts` to add new EV models with specifications.

### Modifying Design System

Update `tailwind.config.ts` for color schemes and `src/app/globals.css` for custom styles.

### Adding New Pages

Create new pages in the `src/app/` directory following the existing structure.

## 📱 Responsive Design

The platform is fully responsive with:

- Mobile-first design approach
- Breakpoints: 768px (tablet) and 1024px (desktop)
- Touch-optimized interactions
- Collapsible navigation for mobile
- Optimized image loading

## 🚀 Future Enhancements

- User authentication and authorization
- Real-time messaging and notifications
- Advanced search with Elasticsearch
- Payment integration for marketplace
- Mobile app development
- API integration with real EV data
- Social features (following, likes, shares)
- Advanced analytics and reporting

---

Built with ❤️ for the EV community
